"use client";
import { useAuth } from "@/context/AuthContext";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import styles from "./styles.module.css";

const Carousel = () => {
  const t = useTranslations("Carousel");
  const [currentSlide, setCurrentSlide] = useState(0);
  const [touchStartX, setTouchStartX] = useState(0);
  const [touchEndX, setTouchEndX] = useState(0);
  const [touchStartY, setTouchStartY] = useState(0);
  const [touchEndY, setTouchEndY] = useState(0);
  const { isLoggedIn, isSubscribed } = useAuth();
  const router = useRouter();

  const carouselData = [
    {
      bannerImage: "/images/webGl/webGlBanner/banner.webp",
      bannerHeading: t("slides.slide1.heading"),
      bannerSubheading: t("slides.slide1.subheading"),
      bannerChildImg: "/images/webGl/webGlBanner/webGlBannerChild.webp",
      redirection: "/get-started",
    },
    {
      bannerImage: "/images/carouselBackground.webp",
      bannerHeading: t("slides.slide2.heading"),
      bannerSubheading: t("slides.slide2.subheading"),
      bannerChildImg: "/images/carouselBannerChild.webp",
      redirection: "/get-started",
    },
    {
      bannerImage: "/images/carouselBackground3.webp",
      bannerHeading: t("slides.slide3.heading"),
      bannerSubheading: t("slides.slide3.subheading"),
      bannerChildImg: "/images/carouselBannerChild3.webp",
      redirection: "/get-started",
    },
    {
      bannerImage: "/images/carouselBackground2.webp",
      bannerHeading: t("slides.slide4.heading"),
      bannerSubheading: t("slides.slide4.subheading"),
      bannerChildImg: "/images/carouselBannerChild2.webp",
      redirection: "/get-started",
    },
  ];

  const handleTouchStart = (e) => {
    setTouchStartX(e.touches[0].clientX);
    setTouchStartY(e.touches[0].clientY);
  };

  const handleTouchMove = (e) => {
    setTouchEndX(e.touches[0].clientX);
    setTouchEndY(e.touches[0].clientY);
  };

  const handleTouchEnd = () => {
    const horizontalSwipeDistance = touchStartX - touchEndX;
    const verticalSwipeDistance = touchStartY - touchEndY;

    if (Math.abs(verticalSwipeDistance) < 50) {
      if (horizontalSwipeDistance > 75) {
        setCurrentSlide((prev) => (prev === carouselData.length - 1 ? 0 : prev + 1));
      } else if (horizontalSwipeDistance < -75) {
        setCurrentSlide((prev) => (prev === 0 ? carouselData.length - 1 : prev - 1));
      }
    }

    setTouchStartX(0);
    setTouchEndX(0);
    setTouchStartY(0);
    setTouchEndY(0);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === carouselData.length - 1 ? 0 : prev + 1));
    }, 10000);
    return () => clearInterval(interval);
  }, []);

  const handleStartLearningClick = (redirectPath) => {
    trackWebEngageEvent("WebGLBannerClk");
    router.push(isSubscribed ? "/user-home-screen" : redirectPath);
  };

  return (
    <div className={styles.carouselWrapper}>
      <div
        className={styles.carousel}
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {carouselData.map((item, index) => (
          <div
            key={index}
            className={`${styles.slide} ${currentSlide === index ? styles.active : ""}`}
            style={{ backgroundImage: `url(${item.bannerImage})` }}
          >
            <div className={styles.carouselContentWrapper}>
              <div className={styles.carouselTextContentWrapper}>
                <h1>{item.bannerHeading}</h1>
                <p>{item.bannerSubheading}</p>
                <button onClick={() => handleStartLearningClick(item.redirection)}>
                  {t("buttons.startLearning")}
                </button>
              </div>
              <div className={styles.carouselImgContentWrapper}>
                <Image
                  src={item.bannerChildImg}
                  layout="fill"
                  objectFit="contain"
                  objectPosition="center"
                  priority
                  alt={t("imageAlts.childParent")}
                />
              </div>
            </div>
          </div>
        ))}
      </div>
      <div className={styles.indicators}>
        {carouselData.map((_, index) => (
          <span
            key={index}
            className={`${styles.indicator} ${currentSlide === index ? styles.active : ""}`}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    </div>
  );
};

export default Carousel;
